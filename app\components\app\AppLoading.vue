<script setup lang="ts">

</script>

<template>
  <div class="w-full fixed top-0 left-0 h-full bg-zinc-900 opacity-80 z-99 flex items-center justify-center">
    <div class="loader" />
  </div>
</template>

<style scoped>
/* HTML: <div class="loader"></div> */
/* HTML: <div class="loader"></div> */
.loader {
  --s: 64px;
  width: var(--s);
  aspect-ratio: 2;
  --_g: #fff 90%,#fff;
  background:
    radial-gradient(farthest-side,var(--_g)) 0   50%/25% 50%,
    radial-gradient(farthest-side at bottom,var(--_g)) 50%  calc(50% - var(--s)/16)/25% 25%,
    radial-gradient(farthest-side at top   ,var(--_g)) 50%  calc(50% + var(--s)/16)/25% 25%,
    radial-gradient(farthest-side at bottom,var(--_g)) 100% calc(50% - var(--s)/16)/25% 25%,
    radial-gradient(farthest-side at top   ,var(--_g)) 100% calc(50% + var(--s)/16)/25% 25%;
  background-repeat: no-repeat;
  animation: l14 1s infinite;
}
@keyframes l14 {
    25%  {background-position:0    50%,50% 0,50% 100%,100% 0,100% 100%}
    50%  {background-position:100% 50%,0   0,0   100%,50%  0,50%  100%}
    75%,
    100% {background-position:100% 50%,0 calc(50% - var(--s)/16),0 calc(50% + var(--s)/16),50% calc(50% - var(--s)/16),50% calc(50% + var(--s)/16)}
}
</style>
