<script setup>
const route = useRoute()
const title = computed(() => route.meta.title)

const router = useRouter()

function back() {
  if (route.path === '/') {
    return
  }
  router.back()
}

function toHome() {
  const appStore = useAppStore()
  appStore.toHome()
}
</script>

<template>
  <div class="w-full sticky top-0 bg-background left-0 flex flex-row h-[50px] bg-content2 items-center justify-center px-1 py-2 z-40 border-b-1 border-default-50">
    <div class="z-0 group relative inline-flex items-center justify-center box-border min-w-10 w-10 h-10" @click="back()">
      <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 40 40" class="fill-current  w-[18px]"><path fill="#EBEBEB" d="M34.96 22.441q0 3.08-1.16 5.8a15.9 15.9 0 0 1-3.18 4.82q-2.02 2.1-4.76 3.44t-5.9 1.62a5 5 0 0 1-.36.06q-.16.02-.36.02h-6.92q-.68 0-1.34-.24t-1.18-.66a3.8 3.8 0 0 1-.86-.98 2.2 2.2 0 0 1-.34-1.16q0-.6.32-1.16a3.2 3.2 0 0 1 .84-.96q.52-.4 1.16-.64a3.7 3.7 0 0 1 1.32-.24l6.2.04q2.12 0 4-.78a10.7 10.7 0 0 0 3.28-2.1 9.7 9.7 0 0 0 2.2-3.1 9.2 9.2 0 0 0 .8-3.82q0-1.92-.72-3.6t-1.98-3a10.2 10.2 0 0 0-2.94-2.14 10.7 10.7 0 0 0-3.64-1.02q-.08 0-.1.02t-.1.02h-6.36v3.16q0 .68-.54 1t-1.74-.4a23 23 0 0 1-1.02-.7q-.7-.5-1.64-1.18t-1.98-1.44l-2-1.52q-.6-.48-1.04-.86a1.2 1.2 0 0 1-.44-.94q0-.48.5-1a13 13 0 0 1 1.14-1.04q.72-.6 1.72-1.4t1.98-1.56 1.76-1.38 1.1-.86q.96-.68 1.56-.28t.64 1.2v3.2h6.36q.2 0 .42.04t.42.08q3.12.32 5.84 1.64t4.74 3.42a15.6 15.6 0 0 1 3.16 4.82 14.75 14.75 0 0 1 1.14 5.76" /></svg>
    </div>
    <span class="flex-1 text-medium font-bold text-center">{{ title }}</span>

    <NuxtLink @click="toHome">
      <button class="z-0 group relative inline-flex items-center justify-center box-border appearance-none select-none whitespace-nowrap font-normal subpixel-antialiased overflow-hidden tap-highlight-transparent data-[pressed=true]:scale-[0.97] outline-none data-[focus-visible=true]:z-10 data-[focus-visible=true]:outline-2 data-[focus-visible=true]:outline-focus data-[focus-visible=true]:outline-offset-2 text-small gap-2 rounded-full px-0 !gap-0 transition-transform-colors-opacity motion-reduce:transition-none bg-transparent text-default-foreground data-[hover=true]:bg-default/40 min-w-10 w-10 h-10" type="button" aria-label="Exit">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 37.97 30.725" class="fill-current  w-[22px]"><symbol id="dialog-home_svg__a" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 37.97 30.725"><path fill="currentColor" d="M37.13 15.9c.53.5.81 1.06.83 1.66.03.61-.17 1.11-.6 1.51-.48.43-1.03.63-1.67.61-.63-.03-1.13-.2-1.51-.53-.12-.1-.51-.45-1.15-1.06s-1.41-1.33-2.31-2.17c-.89-.85-1.85-1.76-2.87-2.73s-1.99-1.88-2.89-2.74c-.91-.86-1.69-1.6-2.35-2.23-.65-.63-1.07-1.02-1.25-1.17-.8-.73-1.6-1.09-2.38-1.06-.78.02-1.55.38-2.31 1.06-.25.22-.72.66-1.41 1.3-.7.65-1.5 1.4-2.41 2.25-.9.86-1.86 1.75-2.87 2.69-1.01.93-1.94 1.8-2.8 2.61-.86.8-1.59 1.49-2.21 2.06-.62.56-.99.9-1.12 1-.37.3-.88.47-1.51.49q-.945.045-1.62-.57c-.51-.45-.75-1-.72-1.64.02-.64.24-1.15.64-1.53.15-.15.62-.59 1.4-1.33.78-.73 1.71-1.6 2.8-2.61 1.08-1 2.25-2.09 3.49-3.25 1.25-1.16 2.41-2.24 3.48-3.25l2.77-2.59c.76-.72 1.21-1.14 1.34-1.27.95-.93 1.99-1.4 3.1-1.41 1.11-.02 2.05.35 2.83 1.11.16.13.47.42.95.89s1.06 1.03 1.76 1.7c.69.67 1.47 1.42 2.32 2.25l2.61 2.52c.88.84 1.75 1.68 2.59 2.51.85.83 1.62 1.58 2.33 2.25.7.67 1.3 1.24 1.78 1.72zm-20.46-5.49c.63-.58 1.39-.89 2.27-.92.89-.04 1.68.27 2.39.92.07.08.3.29.68.65l1.43 1.32c.56.53 1.18 1.12 1.87 1.78.7.65 1.41 1.31 2.14 1.96 1.69 1.57 3.58 3.32 5.67 5.26v7.18c0 .56-.21 1.05-.62 1.48-.42.43-.98.66-1.68.68h-7.15v-5.86c0-.83-.39-1.25-1.17-1.25h-7.23c-.42 0-.72.13-.88.38-.17.25-.25.54-.25.87 0 .15-.01.53-.02 1.15s-.02 1.28-.02 1.99v2.72H7.18c-.73 0-1.31-.16-1.74-.49s-.64-.8-.64-1.4V21.3c2.07-1.89 3.95-3.6 5.63-5.14.71-.65 1.42-1.3 2.12-1.95.71-.64 1.35-1.23 1.93-1.75.58-.53 1.06-.98 1.46-1.35.39-.36.63-.6.73-.7" /></symbol><use href="#dialog-home_svg__a" /></svg>
      </button>
    </NuxtLink>
  </div>
</template>
