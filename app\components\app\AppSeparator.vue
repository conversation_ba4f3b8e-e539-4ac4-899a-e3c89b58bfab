<script setup lang="ts">
import type { HTMLAttributes } from 'vue'

const props = defineProps<{
  label: string
  class?: HTMLAttributes['class']
}>()
</script>

<template>
  <div class="flex items-center gap-4 py-1" :class="props.class">
    <hr class="bg-border border-none w-full h-[1px] flex-1" role="separator"><p class="font-extrabold">
      {{ props.label }}
    </p><hr class="bg-border border-none w-full h-[1px] flex-1" role="separator">
  </div>
</template>
