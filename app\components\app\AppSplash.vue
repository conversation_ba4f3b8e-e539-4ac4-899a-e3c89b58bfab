<script setup lang="ts">
const appStore = useAppStore()
const authStore = useAuthStore()

onMounted(async () => {
  try {
    // check token auth
    await appStore.setAppInit()
    appStore.isInit = true
  }
  catch (error) {
    appStore.isInit = true
  }
})
</script>

<template>
  <div class="w-full h-screen bg-zinc-900 flex items-center justify-center z-999">
    <div class="flex flex-col items-center justify-center gap-6">
      <div class="loader" />
      <div>加载中...</div>
    </div>
  </div>
</template>

<style scoped>
/* HTML: <div class="loader"></div> */
.loader {
  width: 40px;
  aspect-ratio: 1;
  color: #f03355;
  position: relative;
  background:
    conic-gradient(from 134deg at top, currentColor 92deg, #0000 0) top,
    conic-gradient(from -46deg at bottom, currentColor 92deg, #0000 0) bottom;
  background-size: 100% 50%;
  background-repeat: no-repeat;
}

.loader:before {
  content: '';
  position: absolute;
  inset: 0;
  --g: currentColor 14.5px, #0000 0 calc(100% - 14.5px), currentColor 0;
  background:
    linear-gradient(45deg, var(--g)),
    linear-gradient(-45deg, var(--g));
  animation: l7 1.5s infinite cubic-bezier(0.3, 1, 0, 1);
}

@keyframes l7 {
  33% {
    inset: -10px;
    transform: rotate(0deg)
  }

  66% {
    inset: -10px;
    transform: rotate(90deg)
  }

  100% {
    inset: 0;
    transform: rotate(90deg)
  }
}
</style>
