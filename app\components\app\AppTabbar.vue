<script setup>
import UserCodeDialog from './UserCodeDialog.vue'

const route = useRoute()
const router = useRouter()
const currentRoute = computed(() => route.path)
const authStore = useAuthStore()

// 用户码弹窗状态
const showUserCodeDialog = ref(false)

const tabs = ref([
  {
    name: '首页',
    icon: 'mdi:home',
    href: '/',
    show: true,
  },
  {
    name: '分类',
    icon: 'mdi:motion-play',
    href: '/video/category',
    show: true,
  },
  {
    name: '口令码',
    icon: 'mdi:qrcode-scan',
    href: '/user/share',
    show: true,
    special: true, // 标记为特殊样式
  },
  {
    name: '游戏',
    icon: 'mdi:controller',
    href: '/game',
    show: authStore.user.showGame,
  },
  {
    name: '我的',
    icon: 'mdi:account',
    href: '/user',
    show: true,
  },
])

const showTabs = computed(() => {
  return tabs.value.filter(tab => tab.show)
})

const showTabsLength = computed(() => {
  return showTabs.value.length
})

function isActive(href) {
  return currentRoute.value === href
}

const activeTabIndex = computed(() => {
  return tabs.value.findIndex(tab => isActive(tab.href))
})

function handleClick(href, tab) {
  // 如果是用户码模块，直接显示弹窗（不需要登录验证）
  if (tab?.special && href === '/user/share') {
    showUserCodeDialog.value = true
    return
  }

  // 其他需要登录的页面
  if (href === '/user' || href === '/game') {
    if (!authStore.checkAuth()) {
      return
    }
  }
  router.push(href)
}

// function checkActive(path) {
//   return currentRoute.value === path ? 'active' : ''
// }
</script>

<template>
  <div class="fixed bottom-0 bg-background border-t py-2 shadow-lg z-10 h-fit w-full absolute">
    <div class="grid" :class="`grid-cols-${showTabsLength}`">
      <div v-for="(tab) in showTabs" :key="tab.href">
        <div
          class="flex flex-col items-center justify-center relative"
          :class="[
            isActive(tab.href) ? 'text-primary' : 'text-muted-foreground',
            tab.special ? 'special-tab' : ''
          ]"
          @click="handleClick(tab.href, tab)"
        >
          <div class="relative flex flex-col items-center gap-x-0.5">
            <div
              :name="tab.icon"
              class="size-6"
              :class="tab.special ? 'special-icon' : ''"
            />
            <span class="text-xs mt-1">{{ tab.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 用户码弹窗 -->
  <UserCodeDialog v-model:open="showUserCodeDialog" />
</template>

<style scoped>

.tabs {
    @apply inline-flex fixed bottom-0 w-full left-0 z-20 p-1 h-fit items-center 
    flex justify-between border-t-1 bg-background;
}

/* 用户码模块特殊样式 */
.special-icon {
  color: #FFD700 !important; /* 金色 */
}
</style>
