<script setup>
import { cn } from '@/lib/utils'

const props = defineProps({
  color: {
    type: String,
    default: 'gray',
  },
  className: {
    type: String,
    default: '',
  },
})

const colorClasses = {
  rose: 'bg-rose-100 text-rose-600',
  blue: 'bg-blue-100 text-blue-600',
  green: 'bg-green-100 text-green-600',
  amber: 'bg-amber-100 text-amber-600',
  purple: 'bg-purple-100 text-purple-600',
  gray: 'bg-gray-100 text-gray-600',
}
</script>

<template>
  <div :class="cn('w-10 h-10 rounded-full flex items-center justify-center', colorClasses[props.color], props.className)">
    <slot />
  </div>
</template>
