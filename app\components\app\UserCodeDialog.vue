<script setup>
import { ref, computed } from 'vue'

import { toast } from 'vue-sonner'

const props = defineProps({
  open: {
    type: Boolean,
    default: false,
  },
})

const emit = defineEmits(['update:open'])

const authStore = useAuthStore()

const userCode = computed(() => authStore.user.identificationCode)

const inputUserCode = ref('')

const isOpen = computed({
  get: () => props.open,
  set: (value) => emit('update:open', value)
})


// 复制用户码
const { copy } = useCopy()

async function copyUserCode() {
  copy(userCode)
}


async function handleLogin()
{
  if (!inputUserCode.value.trim()) {
     toast.error('请输入口令码')
    return
  }

  try {
    const { data } = await useApiRequest.post('/video/user/login_ma', { uma: inputUserCode.value })
    authStore.isLogin = true
    localStorage.setItem('token', data)
    isOpen.value = false
    inputUserCode.value = ''
    await authStore.getUser()
  } catch (error) {
    console.error('登录失败:', error)
  }
}

</script>

<template>
  test
</template>

<style scoped>
.user-code-display {
  font-size: 2.3rem;
  font-weight: 900;
  color: #FFD700;
  letter-spacing: 0.1em;
  cursor: pointer;
  padding: 1rem;
  border-radius: 12px;
  background: linear-gradient(135deg, rgba(255, 215, 0, 0.1) 0%, rgba(255, 215, 0, 0.05) 100%);
  border: 2px solid rgba(255, 215, 0, 0.3);
  transition: all 0.3s ease;
  animation: glow-pulse 2s ease-in-out infinite alternate;
  position: relative;
  overflow: hidden;
  word-break: break-all;
  max-width: 100%;
  box-sizing: border-box;
}

.user-code-display:hover {
  transform: scale(1.05);
  box-shadow: 0 0 30px rgba(255, 215, 0, 0.4);
  border-color: rgba(255, 215, 0, 0.6);
}

.user-code-display:active {
  transform: scale(0.98);
}

.tip-container {
  animation: fade-in-up 0.6s ease-out 0.3s both;
}

.tip-text {
  font-size: 1rem;
  font-weight: 600;
  color: #FFD700;
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  animation: bounce-gentle 2s ease-in-out infinite;
}

.tip-subtext {
  font-size: 0.875rem;
  color: #888;
  opacity: 0.8;
}

@keyframes glow-pulse {
  0% {
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.2);
  }

  100% {
    box-shadow: 0 0 30px rgba(255, 215, 0, 0.4);
  }
}

@keyframes fade-in-up {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes bounce-gentle {

  0%,
  100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-3px);
  }
}
</style>
