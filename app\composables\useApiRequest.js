import axios from 'axios'
import { toast } from 'vue-sonner'

// 创建 axios 实例
const instance = axios.create({
  baseURL: '',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
})

// 请求拦截器
instance.interceptors.request.use(
  (config) => {

    config.baseURL = useRuntimeConfig().public.apiUrl

    const token = localStorage.getItem('token')

    if (token) {
      config.headers.token = token
    }

    const appStore = useAppStore()

    if (!config?.noLoading) {
      appStore.setLoading(true)
    }

    return config
  },
  (error) => {
    return Promise.reject(error)
  },
)

// 响应拦截器
instance.interceptors.response.use(
  (response) => {
    const appStore = useAppStore()

    const { data } = response
    if (data.code !== 0) {
      toast.error(data.msg)
      appStore.setLoading(false)
      return Promise.reject(data)
    }

    appStore.setLoading(false)
    return data
  },
  (error) => {
    if (error.response?.status === 401) {
      const authStore = useAuthStore()
      authStore.isLogin = false
      authStore.showAuth = true
    }
    else {
      toast.error(error.response?.data?.msg || '请求失败')
    }
    const appStore = useAppStore()
    appStore.setLoading(false)
    return Promise.reject(error)
  },
)

export const useApiRequest = {
  get: (url, params, config) => {
    return instance.get(url, { params, ...config })
  },
  post: (url, data, config) => {
    return instance.post(url,data, config)
  },
}
