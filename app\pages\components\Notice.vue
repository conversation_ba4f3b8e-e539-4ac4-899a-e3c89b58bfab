<script setup>

// 定义消息内容
const message = '请用户牢记自己的口令码，以免用户丢失！    '
</script>

<template>
  <div class="notice-container bg-red-50 dark:bg-red-900/20 border-l-4 border-red-400 dark:border-red-400/50 p-3 mx-4 mt-2 rounded-r-md">
    <div class="flex items-center">
      <Icon name="mdi:information" class="text-red-500 dark:text-red-400 mr-2 flex-shrink-0" />
      <div class="flex-1 overflow-hidden">
        <!-- <Vue3Marquee
          :duration="15"
          :pause-on-hover="true"
          :clone="true"
          class="text-red-700 dark:text-red-300 text-sm font-medium"
        >
          {{ message }}
        </Vue3Marquee> -->
      </div>
    </div>
  </div>
</template>

<style scoped>
.notice-container {
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>
