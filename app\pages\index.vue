<script setup>
import { toast } from 'vue-sonner'
import Carousel from './components/Carousel.vue'
import Notice from './components/Notice.vue'
// import Videos from './components/VideoList.vue'

const authStore = useAuthStore()

onMounted(() => {
  authStore.getUser()
})

definePageMeta({
  hideNavbar: true,
  keepalive: false,
})

const videoRef = ref(null)

function refreshVideo() {
  videoRef.value.refreshList()
}

const showShortName = computed(() => {
  const length = 8
  if (!authStore.user.username) {
    return ''
  }
  if (authStore.user.username.length <= length) {
    return authStore.user.username
  }
  return `${authStore.user.username.slice(0, length)}...`
})
</script>

<template>
  <div class="flex flex-col min-h-screen pb-18">
    <!-- Header -->
    <header class="top-0 z-10 shadow-sm backdrop-blur-md bg-background/90 py-4">
      <div class="flex items-center justify-between px-4">
        <h1 class="text-xl font-bold gradient-text">
          影视星球
        </h1>
        <div class="flex items-center gap-2">
          <button v-if="authStore.user.showDownload" variant="ghost" size="icon" @click="toast('功能开发中')">
            <Icon name="mdi:download-circle" />
          </button>

          <div v-if="authStore.isLogin" class="flex items-center gap-2" @click="$router.replace('/user')">
            <Icon name="mdi:user" />
            {{ showShortName }}
          </div>
        </div>
      </div>

      <div class="relative flex-1 items-center px-4 mt-2" @click="$router.push('/video/search')">
        <Input id="search" readonly placeholder="请输入要查找的视频" type="text" class="pl-10 text-xs" />
        <span class="absolute start-0 inset-y-0 flex items-center justify-center px-8">
          <Icon name="mdi:search" class="size-4 text-muted-foreground" />
        </span>
      </div>
    </header>

    <!-- Main -->

    <main class="flex-1">
      <!-- 轮播图 -->
      <Carousel />

      <!-- 滚动通知 -->
      <Notice />
      <!-- 分类 -->
      <section class="p-4">
        <div class="text-center text-sm text-muted-foreground mb-4 flex items-center justify-center gap-2" @click="refreshVideo">
          刷新视频 <Icon name="mdi:refresh" class="size-4" />
        </div>
        <!-- <Videos ref="videoRef" /> -->
      </section>
    </main>
  </div>


  <AppTabbar />
</template>

<style scoped>

.tags {
    @apply flex flex-wrap gap-2 text-sm;

    .tag {
        @apply px-3 py-1 rounded-md bg-muted text-xs;
    }

    .tag.active {
        @apply bg-blue-500;
    }
}
</style>
