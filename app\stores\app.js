export const useAppStore = defineStore('app', {
  state: () => {
    return {
      isInit: false,
      loading: false,
      theme: '',
      carouselList: [],
    }
  },
  actions: {
    async setAppInit() {
      const authStore = useAuthStore()
      await authStore.autoRegister()

      let token = localStorage.getItem('token')

      if (token) {
        const data = await authStore.checkToken()
        console.log(token)
        
        if (data) {
          await authStore.getUser()
          await authStore.visit()
          await this.checkPayOrder()
        }
      }
    },
    toHome() {
      const router = useRouter()
      let invitationCode = localStorage.getItem('invitationCode')
      let type = localStorage.getItem('type')

      router.replace({
        path: '/',
        query: {
          invitationCode,
          type,
        },
      })
    },
    async checkPayOrder() {
      const { data } = await useApiRequest.get('/video/orders', {
        page: 1,
        pageSize: 10,
        status: 'paid',
      })
      let orders = data.orders || []

      if (orders.length > 0) {
        // 获取第一条订单
        let order = orders[0]
        let now = Math.floor(Date.now() / 1000)
        let paidAt = Math.floor(new Date(order.createTime).getTime() / 1000)

        let diff = now - paidAt
        if (diff <= 3600 * 24) {
          const router = useRouter()
          // 跳转到订单页面
          router.push('/video/order')
        }
      }
      // const { data } = await useApiRequest.post('/user/checkPayOrder')
    },
    setLoading(loading) {
      this.loading = loading
    },
    async getCarouselList() {
      const { data } = await useApiRequest.post('/video/banner/list')
      this.carouselList = data
      localStorage.setItem('carouselList', JSON.stringify(data))
    },
  },
})
