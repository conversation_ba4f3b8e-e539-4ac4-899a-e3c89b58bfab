import { toast } from 'vue-sonner'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    isLogin: false,
    showAuth: false,
    user: JSON.parse(localStorage.getItem('user') || '{}'),
  }),
  actions: {
    async checkToken() {
      const { data } = await useApiRequest.get('/video/user/check-token')
      if (data) {
        this.isLogin = true
        this.showAuth = false
      }
      else {
        this.isLogin = false
        localStorage.removeItem('token')
        // 重新获取邀请码
        const authStore = useAuthStore()
        await authStore.autoRegister()
      }
      return data
    },
    checkAuth() {
      if (this.isLogin) {
        this.showAuth = false
      }
      else {
        this.showAuth = true
      }
      return this.isLogin
    },
    async getUser() {
      let token = localStorage.getItem('token')
      if (!token) return
      const { data } = await useApiRequest.get('/video/user')
      this.user = data

      this.isLogin = true
      this.showAuth = false
    },
    async login(params) {
      const { data } = await useApiRequest.post('/video/user/login', params)
      this.isLogin = true
      this.showAuth = false
      localStorage.setItem('token', data)
      // console.log(data)
    },
    async autoRegister() {
      const router = useRouter()
      const query = router.currentRoute.value.query

      console.log('进入自动注册')

      if (query.platform) {
        localStorage.setItem('platform', query.platform)
      }
      else {
        localStorage.removeItem('platform')
      }

      console.log('获取query参数', query)

      // 获取code和type
      const invitationCode = query.invitationCode
      const type = Number(query.type)

      if (invitationCode ) {
        const cacheCode = localStorage.getItem('invitationCode')
        const token = localStorage.getItem('token')

        //console.log(`当前邀请码: ${invitationCode},缓存邀请码:${cacheCode},当前token:${token}`)

        if (cacheCode === invitationCode && token) {
          console.log('跳过自动注册')
          return
        }

        const params = {
          invitationCode,
          type,
        }
        const { data } = await useApiRequest.post('/video/user/auto-register', params)

        this.isLogin = true
        this.showAuth = false
        localStorage.setItem('token', data)
        localStorage.setItem('invitationCode', invitationCode)
        localStorage.setItem('type', type.toString())
      }
      else {
        console.log('未检测')
        const router = useRouter()
        router.push('/welcome')
      }
    },
    async register(params) {
      const { data } = await useApiRequest.post('/video/user/register', params)
      this.isLogin = true
      this.showAuth = false
      localStorage.setItem('token', data)
    },
    async logout() {
      this.isLogin = false
      this.user = {}
      localStorage.removeItem('user')
      localStorage.removeItem('token')
      const router = useRouter()
      let invitationCode = localStorage.getItem('invitationCode')
      let type = localStorage.getItem('type')
      router.push({
        path: '/',
        query: {
          invitationCode,
          type,
        },
      })
    },
    async visit() {
      const {data} = await useApiRequest.post('/v1/api/visitor')
    }
  },
})
