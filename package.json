{"name": "nuxt-app", "type": "module", "private": true, "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare"}, "dependencies": {"@iconify-json/mdi": "^1.2.3", "@nuxt/icon": "^2.0.0", "@pinia/nuxt": "^0.11.2", "@unocss/reset": "^66.4.2", "axios": "^1.11.0", "nuxt": "^4.0.3", "pinia": "^3.0.3", "vant": "^4.9.21", "vue": "^3.5.18", "vue-router": "^4.5.1", "vue-sonner": "^2.0.2"}, "packageManager": "pnpm@10.11.0+sha512.6540583f41cc5f628eb3d9773ecee802f4f9ef9923cc45b69890fb47991d4b092964694ec3a4f738a420c918a333062c8b925d312f42e4f0c263eb603551f977", "devDependencies": {"@unocss/nuxt": "^66.4.2", "@vant/nuxt": "^1.0.7", "@vueuse/core": "^13.6.0", "@vueuse/nuxt": "^13.6.0", "unocss": "^66.4.2"}}